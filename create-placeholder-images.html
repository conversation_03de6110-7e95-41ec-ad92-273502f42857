<!DOCTYPE html>
<html>
<head>
    <title>Create Placeholder Images</title>
</head>
<body>
    <canvas id="canvas" width="64" height="64"></canvas>
    <script>
        function createPlaceholderImage(text, bgColor, textColor) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, 64, 64);
            
            // 绘制背景
            ctx.fillStyle = bgColor;
            ctx.fillRect(0, 0, 64, 64);
            
            // 绘制文字
            ctx.fillStyle = textColor;
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, 32, 32);
            
            return canvas.toDataURL('image/png');
        }
        
        // 创建头像
        const images = {
            'assistant': createPlaceholderImage('董', '#4a8cff', 'white'),
            'user': createPlaceholderImage('用', '#7c5cff', 'white'),
            'butler': createPlaceholderImage('管', '#ff7b72', 'white')
        };
        
        // 下载图片
        Object.keys(images).forEach(name => {
            const link = document.createElement('a');
            link.download = `${name}.png`;
            link.href = images[name];
            link.click();
        });
    </script>
</body>
</html>
