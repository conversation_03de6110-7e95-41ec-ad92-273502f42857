#!/bin/bash

# 复制天气相关的所有文件从dev分支到当前分支

echo "开始复制天气项目文件..."

# 创建目录结构
mkdir -p src/components/weather
mkdir -p src/components/butler  
mkdir -p src/components/notables
mkdir -p src/services/weather
mkdir -p src/adapters

# 复制组件文件
echo "复制weather组件..."
git show dev:web/src/components/weather/GPSWeatherCard.vue > src/components/weather/GPSWeatherCard.vue
git show dev:web/src/components/weather/InterpretationSection.vue > src/components/weather/InterpretationSection.vue
git show dev:web/src/components/weather/MemoEditorDialog.vue > src/components/weather/MemoEditorDialog.vue
git show dev:web/src/components/weather/MemoListDialog.vue > src/components/weather/MemoListDialog.vue
git show dev:web/src/components/weather/NotablesCard.vue > src/components/weather/NotablesCard.vue
git show dev:web/src/components/weather/BottomVoiceBar.vue > src/components/weather/BottomVoiceBar.vue

echo "复制butler组件..."
git show dev:web/src/components/butler/ButlerComposer.vue > src/components/butler/ButlerComposer.vue
git show dev:web/src/components/butler/ButlerFloat.vue > src/components/butler/ButlerFloat.vue

echo "复制notables组件..."
git show dev:web/src/components/notables/NotableCityCard.vue > src/components/notables/NotableCityCard.vue
git show dev:web/src/components/notables/WeatherKnowledgeSection.vue > src/components/notables/WeatherKnowledgeSection.vue
git show dev:web/src/components/notables/WeatherNotablesView.vue > src/components/notables/WeatherNotablesView.vue

echo "复制common组件..."
mkdir -p src/components/common
git show dev:web/src/components/common/PlayIcon.vue > src/components/common/PlayIcon.vue

echo "复制服务文件..."
git show dev:web/src/services/CityResolver.ts > src/services/weather/CityResolver.ts
git show dev:web/src/services/MemoLocationExtractor.ts > src/services/weather/MemoLocationExtractor.ts
git show dev:web/src/services/WeatherAIInterpretationCoordinator.ts > src/services/weather/WeatherAIInterpretationCoordinator.ts
git show dev:web/src/services/WeatherInterpretationProcessor.ts > src/services/weather/WeatherInterpretationProcessor.ts
git show dev:web/src/services/WeatherNotableService.ts > src/services/weather/WeatherNotableService.ts
git show dev:web/src/services/WeatherService.ts > src/services/weather/WeatherService.ts

echo "复制适配器文件..."
git show dev:web/src/adapters/aiAdapter.ts > src/adapters/aiAdapter.ts
git show dev:web/src/adapters/cityTop100.ts > src/adapters/cityTop100.ts
git show dev:web/src/adapters/fetchAdapter.ts > src/adapters/fetchAdapter.ts
git show dev:web/src/adapters/voiceAdapter.ts > src/adapters/voiceAdapter.ts
git show dev:web/src/adapters/weatherAdapter.ts > src/adapters/weatherAdapter.ts

echo "复制其他文件..."
mkdir -p src/composables
git show dev:web/src/composables > /dev/null 2>&1 || echo "composables目录为空或不存在"

mkdir -p src/data
git show dev:web/src/data > /dev/null 2>&1 || echo "data目录为空或不存在"

echo "文件复制完成！"
