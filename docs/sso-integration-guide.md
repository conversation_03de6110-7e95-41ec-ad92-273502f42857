# SSO集成指导文档

## 概述

本文档基于当前项目的SSO实现，提供快速集成SSO能力的完整指导。项目使用美团内部的SSO系统（`@mtfe/sso-web`）。

## 1. 依赖安装

```bash
npm install @mtfe/sso-web
```

## 2. 核心文件结构

```
src/
├── lib/
│   ├── sso.ts          # SSO配置和初始化
│   ├── http.ts         # HTTP拦截器（401处理）
│   └── fetch.ts        # Fetch封装（401处理）
├── config/
│   └── index.ts        # 环境配置
├── apis/
│   └── common.ts       # 用户信息API
└── main.ts             # 应用入口（SSO登录逻辑）
```

## 3. 配置文件设置

### 3.1 环境配置 (`src/config/index.ts`)

```typescript
const env = <'dev' | 'test' | 'product'>(
  (process.env.VUE_APP_ENV === 'production' ? 'product' : 'test')
);

// SSO服务地址配置
const ssoHostList = {
  dev: 'https://ssosv.it.test.sankuai.com/sson/login',
  test: 'https://ssosv.it.test.sankuai.com/sson/login',
  product: 'https://ssosv.sankuai.com/sson/login',
};

// SSO客户端ID配置
const ssoClientIdList = {
  dev: '3b08c215c9',        // 测试环境客户端ID
  test: '3b08c215c9',       // 测试环境客户端ID
  product: '12d702aa62',    // 生产环境客户端ID
};

// SSO访问环境配置
const ssoAccessEnvIdList = {
  dev: 'test',
  test: 'test',
  product: 'product',
};

export default {
  env,
  ssoHost: ssoHostList[env],
  ssoClientId: ssoClientIdList[env],
  ssoAccessEnv: ssoAccessEnvIdList[env],
};
```

### 3.2 SSO初始化 (`src/lib/sso.ts`)

```typescript
import SSOWeb from '@mtfe/sso-web';
import config from '@/config/index';

/**
 * SSO配置选项
 * 注意：需要业务后端先申请SSO资源，前端替换接口地址与clientId
 * SSO接入指南：https://km.sankuai.com/page/13024494
 * SSO申请资源指南：https://km.sankuai.com/page/368775561
 */
export const ssoOption = {
  clientId: config.ssoClientId,
  accessEnv: config.ssoAccessEnv, // test or product
  // rewriteLocation: '',          // 可选：重写登录后跳转地址
  // callbackUrl: '/callback',     // 可选：回调地址
};

export default SSOWeb(ssoOption);
```

## 4. 应用入口集成

### 4.1 主入口文件 (`src/main.ts`)

```typescript
import { createApp } from 'vue';
import SSOWeb from '@/lib/sso';
import { getUserInfo } from '@/apis/common';
import instance from '@/lib/axios';
import fetchInstance from '@/lib/fetch';

const app = createApp(App);

// SSO登录流程
SSOWeb.login()
  .then(async (ssoid: unknown) => {
    if (ssoid && typeof ssoid === 'string') {
      // 1. 设置axios请求头
      instance.defaults.headers.common['access-token'] = ssoid;
      
      // 2. 设置fetch实例token
      fetchInstance.setAccessToken(ssoid);
      
      // 3. 获取用户信息
      const userData = await getUserInfo();
      
      // 4. 初始化应用（路由、状态管理等）
      app.use(pinia);
      app.use(router);
      app.mount('#app');
    }
  })
  .catch((err) => {
    console.error('SSO登录失败:', err);
  });
```

## 5. HTTP请求拦截器

### 5.1 Axios拦截器 (`src/lib/http.ts`)

```typescript
import ssoWeb from './sso';

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    // 处理401未授权
    if (response.data.status === 401 || response.data?.code === 401) {
      window.location.href = ssoWeb.getLoginUrl();
    }
    return response;
  },
  (error) => {
    // 处理HTTP 401错误
    if (error.response && error.response.status === 401) {
      window.location.href = ssoWeb.getLoginUrl();
    }
    return Promise.reject(error);
  }
);
```

### 5.2 Fetch封装 (`src/lib/fetch.ts`)

```typescript
import ssoWeb from '@/lib/sso';

class FetchSingleton {
  public static accessToken: string;
  
  // 设置访问令牌
  setAccessToken(ssoid: string) {
    FetchSingleton.accessToken = ssoid;
  }
  
  async fetch(url: string, options: RequestInit = {}) {
    // 添加认证头
    const defaultHeaders = {
      'access-token': FetchSingleton.accessToken,
      'Content-Type': 'application/json',
    };
    
    const response = await fetch(url, {
      headers: { ...defaultHeaders, ...options.headers },
      ...options,
    });
    
    const res = await response.json();
    
    // 处理401未登录
    if (res?.status === 401) {
      window.location.href = ssoWeb.getLoginUrl();
      return;
    }
    
    return res;
  }
}
```

## 6. 用户信息API

### 6.1 获取用户信息 (`src/apis/common.ts`)

```typescript
import http from '@/lib/http';

export interface IUserInfo {
  login: string;  // 用户登录名
  name: string;   // 用户姓名
}

// 获取当前登录用户信息
export function getUserInfo(): Promise<IUserInfo> {
  return http({
    url: '/web/v1/sso',
    method: 'get',
  });
}
```

## 7. SSO集成层级说明

### 7.1 应用级别集成（一次性配置）
SSO是在**应用级别**进行集成的，不需要每个页面单独处理：

```
用户访问 → SSO验证 → 应用启动 → 所有页面可用
```

- **入口统一**：在 `main.ts` 中进行SSO登录
- **全局保护**：整个应用都受SSO保护
- **页面透明**：各个页面组件无需关心SSO逻辑

### 7.2 页面访问流程
```mermaid
graph TD
    A[用户访问任意页面] --> B[SSO.login检查]
    B --> C{是否已登录?}
    C -->|否| D[跳转SSO登录页]
    C -->|是| E[获取用户信息]
    E --> F[应用启动]
    F --> G[显示目标页面]
    D --> H[用户登录]
    H --> E
```

### 7.3 各层级职责
- **应用入口**：负责SSO登录和token设置
- **HTTP拦截器**：负责401错误处理和重定向
- **页面组件**：专注业务逻辑，无需处理认证

## 8. 快速集成步骤

### 步骤1：申请SSO资源
1. 访问 [SSO申请资源指南](https://km.sankuai.com/page/368775561)
2. 申请对应环境的clientId
3. 获取SSO服务地址

### 步骤2：安装依赖
```bash
npm install @mtfe/sso-web
```

### 步骤3：配置环境参数
- 更新 `src/config/index.ts` 中的SSO配置
- 替换clientId为申请到的值

### 步骤4：创建SSO实例
- 创建 `src/lib/sso.ts` 文件
- 配置SSO选项

### 步骤5：集成到应用入口
- 在 `src/main.ts` 中添加SSO登录逻辑
- 设置token到HTTP请求头

### 步骤6：添加HTTP拦截器
- 在axios/fetch拦截器中处理401状态码
- 自动跳转到SSO登录页面

### 步骤7：实现用户信息获取
- 创建获取用户信息的API接口
- 在登录成功后调用获取用户信息

## 8. 重要提醒：页面无需单独集成SSO

### 8.1 常见误区
❌ **错误认知**：每个页面都需要单独集成SSO
✅ **正确理解**：SSO是应用级别的一次性集成

### 8.2 实际情况
- **页面组件**：只需要专注业务逻辑
- **路由跳转**：无需额外的认证处理
- **API调用**：自动携带认证信息
- **权限控制**：通过HTTP拦截器统一处理

### 8.3 开发体验
```typescript
// 页面组件中，直接调用API即可
export default {
  async mounted() {
    // 无需关心认证，直接调用业务API
    const data = await getUserProfile();
    this.userProfile = data;
  }
}
```

## 9. 注意事项

1. **环境区分**：确保不同环境使用对应的clientId和SSO地址
2. **错误处理**：妥善处理SSO登录失败的情况
3. **Token管理**：确保token正确设置到所有HTTP请求中
4. **登出处理**：需要时可调用 `ssoWeb.logout()` 进行登出
5. **回调处理**：根据需要配置callbackUrl和rewriteLocation

## 10. 常见问题

### Q: 如何处理SSO登录失败？
A: 在 `SSOWeb.login().catch()` 中添加错误处理逻辑，可以显示错误提示或跳转到错误页面。

### Q: 如何实现登出功能？
A: 调用 `ssoWeb.logout()` 方法，会清除本地token并跳转到SSO登出页面。

### Q: 如何在开发环境跳过SSO？
A: 可以在开发环境中mock SSO登录，直接返回测试token。

## 10. 相关资源

- [SSO接入指南](https://km.sankuai.com/page/13024494)
- [SSO申请资源指南](https://km.sankuai.com/page/368775561)
- 技术支持：suweijie02，songchao09，zhangshibo02
