<template>
  <main style="height:100%; display:flex; flex-direction:column;">
    <!-- 简化的头部 -->
    <div class="weather-header">
      <h1>董天奇 - 天气助手</h1>
      <p>{{ city }} 天气信息</p>
    </div>

    <div style="flex:1; overflow:auto; padding:16px;">
      <!-- 基础天气信息 -->
      <div class="weather-card">
        <h2>当前天气</h2>
        <p v-if="loading">加载中...</p>
        <div v-else-if="current">
          <p>温度: {{ current.temperature }}°C</p>
          <p>天气: {{ current.condition }}</p>
          <p>湿度: {{ current.humidity }}%</p>
        </div>
        <p v-else>暂无天气数据</p>
      </div>

      <!-- 备忘录区域 -->
      <div class="memo-card">
        <h2>备忘录</h2>
        <p>懂你，服务会更好</p>
        <div class="memo-actions">
          <button @click="addMemo">添加备忘录</button>
          <button @click="showMemoList">查看列表</button>
        </div>
      </div>

      <!-- AI 解读区域 -->
      <div class="ai-card">
        <h2>AI 深解读</h2>
        <p v-if="aiLoading">AI正在分析天气数据...</p>
        <div v-else-if="aiText">
          <p>{{ aiText }}</p>
        </div>
        <p v-else>点击刷新获取AI解读</p>
        <button @click="refreshAI" :disabled="aiLoading">
          {{ aiLoading ? '分析中...' : '刷新解读' }}
        </button>
      </div>
    </div>

    <!-- 底部操作区 -->
    <div class="bottom-actions">
      <button @click="refreshWeather" :disabled="loading">
        {{ loading ? '刷新中...' : '刷新天气' }}
      </button>
      <button @click="goBack">返回聊天</button>
    </div>
  </main>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

// 基础状态
const city = ref('北京')
const loading = ref(false)
const current = ref<any>(null)
const aiLoading = ref(false)
const aiText = ref('')

const router = useRouter()

// 模拟天气数据
const mockWeatherData = {
  temperature: 22,
  condition: '晴',
  humidity: 45
}

// 模拟AI解读
const mockAITexts = [
  '今日天气晴朗，温度适宜，是外出活动的好时机。建议穿着轻薄衣物，注意防晒。',
  '当前湿度较低，空气干燥，建议多补充水分，使用保湿护肤品。',
  '天气条件良好，适合户外运动，但请注意紫外线防护。'
]

// 刷新天气
const refreshWeather = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    current.value = mockWeatherData
  } catch (error) {
    console.error('获取天气失败:', error)
  } finally {
    loading.value = false
  }
}

// 刷新AI解读
const refreshAI = async () => {
  aiLoading.value = true
  try {
    // 模拟AI分析
    await new Promise(resolve => setTimeout(resolve, 2000))
    const randomIndex = Math.floor(Math.random() * mockAITexts.length)
    aiText.value = mockAITexts[randomIndex]
  } catch (error) {
    console.error('AI解读失败:', error)
  } finally {
    aiLoading.value = false
  }
}

// 备忘录操作
const addMemo = () => {
  alert('添加备忘录功能开发中...')
}

const showMemoList = () => {
  alert('备忘录列表功能开发中...')
}

// 返回聊天
const goBack = () => {
  router.push({ name: 'chat' })
}

// 页面加载时获取天气
onMounted(() => {
  refreshWeather()
})
</script>

<style scoped>
.weather-header {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: white;
  padding: 20px;
  text-align: center;
}

.weather-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
}

.weather-header p {
  margin: 0;
  opacity: 0.9;
}

.weather-card, .memo-card, .ai-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.weather-card h2, .memo-card h2, .ai-card h2 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 18px;
}

.memo-actions {
  display: flex;
  gap: 12px;
  margin-top: 12px;
}

.bottom-actions {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
}

button {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  background: #007bff;
  color: white;
  cursor: pointer;
  font-size: 14px;
}

button:hover:not(:disabled) {
  background: #0056b3;
}

button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.memo-actions button {
  flex: 1;
  background: #28a745;
}

.memo-actions button:hover {
  background: #1e7e34;
}
</style>
