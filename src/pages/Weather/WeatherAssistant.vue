<template>
  <main class="weather-main theme-background">
    <div class="weather-container">
      <!-- 使用MaintopBar组件替代HeaderBar -->
      <Maintopbar
        :show-back-btn="false"
        :show-history-btn="false"
        :show-relationship-btn="false"
        :show-user-avatar="false"
        :show-assistant-avatar="true"
        :assistant-avatar-src="weatherIcon"
        :assistant-name="'董天气'"
        :selected-assistant-avatar="weatherIcon"
        :show-voice-btn="false"
        :show-add-chat-btn="false"
        :show-weather-btn="false"
        :show-memo-btn="false"
        :show-home-btn="false"
        :show-back-to-index-btn="true"
        :show-feature-intro="true"
        add-chat-type="add"
        :is-chat-play="false"
        :user-loading="false"
        :current-mis-id="''"
        :get-random-color="() => '#ccc'"
        :get-avatar-letter="() => ''"
        :show-header-grad="false"
        @back-to-index="onBackToIndex"
      />

      <div class="content-area">
      <!-- 备忘录区（模块化） -->
      <MemoSection />

      <!-- GPS 天气卡片（模块化，复用当前城市结果做占位） -->
      <GPSWeatherCard :weather="current" :loading="loading" :error="error" />

      <!-- 划重点（仅在视图内部展开时显示“天气知识”） -->
      <WeatherNotablesView
        :groups="groupedNotables"
        :loading="loadingNotables"
        :loading-steps="notablesSteps"
        empty-reason="暂无关注城市，请在上方备忘录添加城市关键词（如：北京、上海）"
      />

      <!-- AI 深解读（对齐 iOS：加载步骤/多城市卡片/单条文本） -->
      <InterpretationSection
        :loading="aiLoading"
        :loading-finished="aiFinished"
        :loading-steps="aiSteps"
        :city-interpretations="aiCities"
        :interpretation-text="aiText"
      />

      </div>

      <!-- Butler 通用输入框（与悬浮管家互斥显示） -->
      <div ref="composerContainer" class="composer-area">
        <ButlerComposer @send="onSend" />
      </div>

      <ButlerFloat
        v-if="butlerFloatVisible"
        :message="butlerTip"
        @click="onButlerFloatClick"
      />
    </div>
  </main>
</template>

<script setup lang="ts">
// iOS 天气服务对齐的适配层 + 头部组件
import { ref, onMounted, watch, computed, onBeforeUnmount } from 'vue'
import '@/styles/theme.css'
import Maintopbar from '@/components/Maintopbar.vue'
import MemoSection from '@/components/weather/MemoSection.vue'
// 旧 NotablesCard 保留在其他页面备用，这里改用新的视图组件三件套
import WeatherNotablesView from '@/components/notables/WeatherNotablesView.vue'
import InterpretationSection from '@/components/weather/InterpretationSection.vue'
import GPSWeatherCard from '@/components/weather/GPSWeatherCard.vue'
// Butler 组件
import ButlerComposer from '@/components/butler/ButlerComposer.vue'
import ButlerFloat from '@/components/butler/ButlerFloat.vue'
import { butlerStore } from '@/stores/butlerStore'
import { getLocationIdForCity, fetchCurrentWeather, fetchCurrentWeatherByCoord, type CurrentWeather } from '@/adapters/weatherAdapter'
import { WeatherService } from '@/services/weather/WeatherService'
import { WeatherAIInterpretationCoordinator } from '@/services/weather/WeatherAIInterpretationCoordinator'
import { streamWeatherPrompt, runWeatherPrompt } from '@/adapters/aiAdapter'
import { WeatherNotableService } from '@/services/weather/WeatherNotableService'
import { extractCitiesFromMemos } from '@/services/weather/MemoLocationExtractor'
import { memoStore } from '@/stores/memoStore'
import { useRouter } from 'vue-router'
import weatherIcon from '@/assets/assistant/weather.jpg'

const router = useRouter()
const city = ref('北京')
const loading = ref(false)
const error = ref('')
const current = ref<CurrentWeather | null>(null)
const loadingNotables = ref(false)
const notablesSteps = ref<string[]>([])
const groupedNotables = ref<Array<{ city: string; notices: string[] }>>([])

// 服务实例（简单单例就地使用）
const weatherService = new WeatherService()
const notableService = new WeatherNotableService()
const aiCoordinator = new WeatherAIInterpretationCoordinator()

// —— 代理计算属性：将 ref 暴露为普通值，便于传入子组件 props（满足 TS 校验）
const aiLoading = computed(() => aiCoordinator.isLoading.value)
const aiFinished = computed(() => aiCoordinator.loadingState.value.isFinished)
const aiSteps = computed(() => aiCoordinator.loadingState.value.steps)
const aiCities = computed(() => aiCoordinator.cityInterpretations.value)
const aiText = computed(() => aiCoordinator.weatherInterpretation.value)
// 已移除调试日志面板

function handleNoCities() {
  // 清空服务与界面状态（对齐 iOS：当无关注城市时，模块展示空态）
  weatherService.clear()
  notableService.clear()
  aiCoordinator.reset()
  groupedNotables.value = []
  notablesSteps.value = []
}

const onQuery = async () => {
  error.value = ''
  current.value = null
  loading.value = true
  try {
    // 优先尝试 GPS（与 iOS 当前地点一致）
    const byGPS = await tryQueryByGPS()
    if (byGPS) return

    const id = getLocationIdForCity(city.value)
    if (!id) {
      error.value = '暂不支持该城市，请输入：北京/上海/广州/深圳（后续接入POI）'
      return
    }
    const cw = await fetchCurrentWeather(id)
    if (!cw) {
      error.value = '未获取到当前天气'
      return
    }
    current.value = cw

    // 刷新划重点：若备忘录有城市则按其刷新；否则清空模块展示空态
    const memoCities = resolveCitiesFromMemos()
    if (memoCities && memoCities.length) {
      await refreshNotables(memoCities)
    } else {
      handleNoCities()
    }
  } catch (e: any) {
    error.value = e?.message || '查询失败，请稍后再试'
  } finally {
    loading.value = false
  }
}

async function tryQueryByGPS(): Promise<boolean> {
  if (!('geolocation' in navigator)) return false
  try {
    const pos = await new Promise<GeolocationPosition>((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(resolve, reject, { enableHighAccuracy: true, timeout: 8000, maximumAge: 60000 })
    })
    const { latitude, longitude } = pos.coords
    const cw = await fetchCurrentWeatherByCoord(latitude, longitude)
    if (!cw) {
      error.value = '未获取到当前地点天气'
      return true
    }
    current.value = cw
    // GPS 命中后依然严格以备忘录城市为准；无城市则显示空态
    const memoCities = resolveCitiesFromMemos()
    if (memoCities && memoCities.length) {
      await refreshNotables(memoCities)
    } else {
      handleNoCities()
    }
    return true
  } catch (err: any) {
    // 用户拒绝/超时/失败，回退城市查询
    return false
  }
}



onMounted(() => {
  // 页面首次进入尝试 GPS
  onQuery()
  // 互斥显示：监听 Composer 容器是否在视区
  setupComposerObserver()
})

onBeforeUnmount(() => {
  if (observer) observer.disconnect()
})

function resolveCitiesFromMemos(): string[] | null {
  const texts = memoStore.list.map((m: any) => m.text).filter(Boolean)
  const cities = extractCitiesFromMemos(texts)
  return cities.length ? cities : null
}

async function refreshNotables(cities: string[]) {
  loadingNotables.value = true
  notablesSteps.value = ['正在分析重点城市']
  try {
    weatherService.setQueriedCities(cities)
    notablesSteps.value = [...notablesSteps.value, '正在抓取天气数据']
    await weatherService.fetchAllByCities(cities)
    notablesSteps.value = [...notablesSteps.value, '正在提炼关键信息']
    await notableService.refreshNotables(weatherService)
    groupedNotables.value = notableService.groupedNotices
    notablesSteps.value = [...notablesSteps.value, '完成：生成城市卡片']
    // 刷新 AI 深解读
    await refreshInterpretation(cities)
  } finally {
    loadingNotables.value = false
  }
}

// 监听备忘录变化，驱动划重点刷新
watch(
  () => memoStore.list.map((m: any) => m.text),
  async () => {
    const cities = resolveCitiesFromMemos()
    if (cities && cities.length) {
      await refreshNotables(cities)
    } else {
      handleNoCities()
    }
  },
  { deep: true }
)

async function refreshInterpretation(cities: string[]) {
  const texts = memoStore.list.map((m: any) => m.text).filter(Boolean)
  // 注入 AI 执行器（支持流式；如需非流式可改为 prompt => runWeatherPrompt(prompt)）
  // 记录当前环境变量（仅开发日志）
  try {
    // @ts-ignore
    const env: any = import.meta.env || {}
    aiCoordinator['debugLogs'].value = [
      ...aiCoordinator['debugLogs'].value,
      `[${new Date().toISOString()}] ENV VITE_AI_API_BASE=${env.VITE_AI_API_BASE || '(empty)'} VITE_AI_MODEL=${env.VITE_AI_MODEL || '(empty)'} hasKey=${env.VITE_AI_API_KEY ? 'yes' : 'no'}`,
    ]
  } catch {}
  await aiCoordinator.generateWeatherInterpretation(
    cities,
    weatherService,
    texts,
    (prompt) => streamWeatherPrompt(prompt),
    {
      currentDate: new Date().toISOString(),
      currentLocationInfo: `当前查询城市：${city.value}`,
      locationAnalysis: '基于备忘录抽取的关注城市集合',
      // 若后续提供自然语言化的天气摘要，可注入 weatherDataNL 以完全对齐 iOS 模板
    }
  )
}

// —— Butler 互斥显示逻辑 ——
const composerContainer = ref<HTMLElement | null>(null)
let observer: IntersectionObserver | null = null
const butlerFloatVisible = computed(() => butlerStore.floatVisible.value)
const butlerTip = computed(() => butlerStore.tip.value || '需要我帮你组织一个回答吗？点我回到底部输入')

function setupComposerObserver() {
  if (!('IntersectionObserver' in window)) return
  if (observer) observer.disconnect()
  observer = new IntersectionObserver((entries) => {
    const e = entries[0]
    if (!e) return
    if (e.isIntersecting) {
      butlerStore.showComposer()
    } else {
      butlerStore.showFloat()
    }
  }, { threshold: 0.2 })
  if (composerContainer.value) observer.observe(composerContainer.value)
}

function onButlerFloatClick() {
  // 回到底部并显示 Composer
  butlerStore.showComposer()
  window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' })
}

function onSend(text: string) {
  // 占位：将来在此把消息分发到当前助手
  console.log('[Butler send]', text)
}
function onBackToIndex() {
  // 返回首页 - 跳转到index.vue页面
  router.push({ name: 'chat' })
}

function onOpenHub() {
  // 占位：返回“助手大厅”页。若项目使用 vue-router，这里可以改为：router.push({ name: 'AssistantsHub' })
  alert('查看更多助手')
}
</script>

<style scoped>
.weather-main {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.weather-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
  padding: 0 var(--spacing-lg);
  box-sizing: border-box;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  padding: var(--spacing-xl) 0;
  min-height: 0;
}

.composer-area {
  padding: var(--spacing-lg) 0;
  margin-top: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .weather-container {
    padding: 0 var(--spacing-md);
  }

  .content-area {
    gap: var(--spacing-lg);
    padding: var(--spacing-lg) 0;
  }
}
</style>
