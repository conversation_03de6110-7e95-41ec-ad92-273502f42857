<template>
  <div class="weather-assistant">
    <h1>董天奇 - 天气助手</h1>
    <p>欢迎使用天气助手功能！</p>
    <p>当前城市: {{ city }}</p>
    <button @click="goBack">返回聊天</button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const city = ref('北京')
const router = useRouter()

const goBack = () => {
  router.push({ name: 'chat' })
}
</script>

<style scoped>
.weather-assistant {
  padding: 20px;
  text-align: center;
}

h1 {
  color: #333;
  margin-bottom: 20px;
}

button {
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

button:hover {
  background: #0056b3;
}
</style>
