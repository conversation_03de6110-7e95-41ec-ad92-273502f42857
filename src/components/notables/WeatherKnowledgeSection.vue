<template>
  <section class="knowledge warm-card">
    <!-- 标题（与 iOS 一致） -->
    <div class="header">
      <span class="bulb">💡</span>
      <span class="title">天气知识</span>
      <span class="spacer" />
    </div>

    <!-- 标签区，三行布局（顺序与 iOS 一致） -->
    <div class="tags">
      <div class="row">
        <button
          v-for="idx in row1"
          :key="tips[idx].title"
          class="chip"
          :aria-pressed="selectedTitle === tips[idx].title"
          @click="toggle(tips[idx].title)"
        >
          <span class="chip-icon">●</span>
          <span class="chip-text">{{ tips[idx].title }}</span>
        </button>
      </div>
      <div class="row">
        <button
          v-for="idx in row2"
          :key="tips[idx].title"
          class="chip"
          :aria-pressed="selectedTitle === tips[idx].title"
          @click="toggle(tips[idx].title)"
        >
          <span class="chip-icon">●</span>
          <span class="chip-text">{{ tips[idx].title }}</span>
        </button>
      </div>
      <div class="row">
        <button
          v-for="idx in row3"
          :key="tips[idx].title"
          class="chip"
          :aria-pressed="selectedTitle === tips[idx].title"
          @click="toggle(tips[idx].title)"
        >
          <span class="chip-icon">●</span>
          <span class="chip-text">{{ tips[idx].title }}</span>
        </button>
      </div>
    </div>

    <!-- 详情：仅在选中时显示（单选切换） -->
    <div v-if="selectedTitle" class="detail">
      <div class="detail-header">
        <span class="detail-icon">🔎</span>
        <span class="detail-title">{{ selectedTitle }}</span>
        <button class="close" @click="toggle(selectedTitle!)">收起</button>
      </div>
      <div class="detail-exp">{{ selectedTip?.explanation }}</div>
    </div>
  </section>
</template>

<script setup lang="ts">
// 按 iOS WeatherNotablesView.swift 的 WeatherKnowledgeTip 列表逐条同步
import { computed, ref } from 'vue'

interface Tip { title: string; explanation: string; icon?: string }

const tips: Tip[] = [
  {
    title: '体感温度',
    explanation:
      '考虑了湿度、风速等因素后，人体实际感受到的温度。比如30摄氏度但湿度很高时，体感温度可能达到35摄氏度，让人感觉更热。该数值很难提前多日预测，因此只有实时数据才有。',
    icon: 'thermometer',
  },
  {
    title: '降水概率',
    explanation:
      '未来某时段内下雨的可能性百分比。70%以上建议带伞，50%以下基本不会下雨。这是基于气象模型计算的统计概率。',
    icon: 'cloud.rain',
  },
  {
    title: '降水量',
    explanation:
      '默认1小时，小雨<2.5毫米，中雨2.5-8毫米，大雨8-15毫米，暴雨>15毫米。北京年日均降水量不到1.5毫米、7月份日均约6毫米；深圳年日均约6毫米，8月日均约10毫米',
    icon: 'drop',
  },
  {
    title: '紫外线指数',
    explanation:
      '0到2弱，3到5中等，6到7强，8到10很强，11+极强。超过6需要防晒，10点到14点紫外线最强，建议避免户外活动。',
    icon: 'sun.max',
  },
  {
    title: '露点',
    explanation:
      '空气中水蒸气开始凝结成水滴的温度。露点越接近气温，空气越潮湿。露点大于24摄氏度非常闷热，小于10摄氏度比较干燥舒适。',
    icon: 'drop.triangle',
  },
  {
    title: '湿度',
    explanation:
      '空气中水蒸气的含量百分比。40%到60%最舒适，大于80%感觉闷热潮湿，小于30%感觉干燥，可能引起皮肤不适。',
    icon: 'drop.fill',
  },
  {
    title: '气压',
    explanation:
      '标准气压1013百帕。高压大于1020百帕天气晴朗，低压小于1000百帕多云下雨。气压变化快时，敏感人群可能感到不适。',
    icon: 'gauge',
  },
  {
    title: '云量',
    explanation:
      '天空被云遮盖的百分比。小于25%晴天，25%到75%多云，大于75%阴天。云量影响日照和紫外线强度，多云时紫外线会被削弱。注意，夏天云量变化快，天气预报更新慢，该数值可能与您当前观察不一致。',
    icon: 'cloud',
  },
  {
    title: '能见度',
    explanation:
      '正常视力的人能够看清目标轮廓的最大距离。大于10公里极佳，5到10公里良好，2到5公里一般，小于2公里较差。能见度低时注意出行安全。',
    icon: 'eye',
  },
  {
    title: '风向',
    explanation:
      '风吹来的方向，如北风指风从北方吹来。风向影响体感温度，北风通常较冷，南风较暖。风向变化常预示天气变化。',
    icon: 'wind',
  },
]

// iOS 标签布局顺序：
// 第一行：0 1 2 8
// 第二行：3 4 5 9
// 第三行：6 7
const row1 = [0, 1, 2, 8]
const row2 = [3, 4, 5, 9]
const row3 = [6, 7]

const selectedTitle = ref<string | null>(null)
const selectedTip = computed(() => tips.find(t => t.title === selectedTitle.value))

function toggle(title: string) {
  selectedTitle.value = selectedTitle.value === title ? null : title
}

// 无子组件，直接在模板中渲染标签按钮
</script>

<style scoped>
.knowledge { padding: 8px; }
.header { display: flex; align-items: center; gap: 6px; color: #000000; }
.bulb { font-size: 14px; }
.title { font-size: 14px; font-weight: 600; }
.spacer { margin-left: auto; }

.tags { display: flex; flex-direction: column; gap: 6px; padding-top: 6px; }
.row { display: flex; gap: 6px; flex-wrap: nowrap; }
.chip { display: inline-flex; align-items: center; gap: 4px; padding: 6px 8px; border-radius: 16px; border: 1px solid var(--border); background: rgba(0,0,0,0); color: #000000; font-size: 12px; }
.chip[aria-pressed="true"] { background: color-mix(in srgb, var(--notable-accent, #6aa) 10%, transparent); border-color: color-mix(in srgb, var(--notable-accent, #6aa) 30%, var(--border)); color: #000000; }
.chip-icon { font-size: 10px; opacity: 0.9; }
.chip-text { font-size: 12px; }

.detail { margin-top: 8px; padding: 10px; background: var(--card-bg); border: 1px solid var(--border); border-radius: 8px; }
.detail-header { display: flex; align-items: center; gap: 6px; }
.detail-icon { font-size: 14px; color: #000000; }
.detail-title { font-size: 13px; font-weight: 600; color: #000000; }
.close { margin-left: auto; font-size: 12px; color: #000000; background: transparent; border: none; }
.detail-exp { margin-top: 4px; font-size: 12px; color: #000000; line-height: 1.5; }
</style>
