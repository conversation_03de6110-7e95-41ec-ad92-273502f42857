<template>
  <section class="notables warm-card">
    <div class="header">
      <h3 class="title">划重点</h3>
      <span class="subtitle">信息太多先抓关键</span>
      <div class="actions">
        <button class="icon" title="播放（暂未开放）" disabled>▶︎</button>
        <button class="icon" title="魔法棒（占位）" disabled>✨</button>
      </div>
    </div>

    <ul class="list">
      <li v-for="(n, idx) in items" :key="idx" class="row">
        <span class="dot" />
        <span class="text">{{ n }}</span>
      </li>
    </ul>

    <div v-if="loading" class="skeleton">
      <div class="s-line" v-for="i in 3" :key="i" />
    </div>
  </section>
</template>

<script setup lang="ts">
interface Props {
  items?: string[]
  loading?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  items: () => [
    '今日午后有阵雨，外出备伞',
    '紫外线偏强，注意防晒',
    '晚间风力增强，注意保暖',
  ],
  loading: false,
})
</script>

<style scoped>
.notables { padding: 8px; }
.header { display: flex; align-items: flex-end; gap: 6px; }
.title { margin: 0; font-size: 15px; font-weight: 700; color: #000000; }
.subtitle { font-size: 12px; color: #000000; opacity: 0.9; }
.actions { margin-left: auto; display: flex; gap: 8px; }
.icon {
  width: 24px; height: 24px;
  display: inline-flex; align-items: center; justify-content: center;
  border-radius: 8px; border: 1px solid var(--border);
  color: #000000; background: var(--card-bg); opacity: 0.6;
}
.list { margin: 8px 0 0; padding: 0; list-style: none; display: grid; gap: 8px; }
.row { display: flex; gap: 8px; align-items: flex-start; }
.dot { width: 6px; height: 6px; border-radius: 50%; background: #ff9e57; margin-top: 6px; flex: 0 0 auto; }
.text { color: #000000; font-size: 14px; line-height: 1.4; }
.skeleton { margin-top: 8px; display: grid; gap: 6px; }
.s-line { height: 10px; border-radius: 6px; background: linear-gradient(90deg, rgba(0,0,0,0.06), rgba(0,0,0,0.12), rgba(0,0,0,0.06)); }
</style>
