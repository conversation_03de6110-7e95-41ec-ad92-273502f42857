<template>
  <footer :style="barStyle" class="voice-bar">
    <div class="inner warm-card">
      <button class="mic" disabled title="录音占位（后续接入）">🎙️</button>
      <div class="hint">语音功能稍后开放（保持与 iOS 同步）</div>
      <button class="send" disabled title="发送（占位）">➤</button>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { computed, type CSSProperties } from 'vue'

const barStyle = computed<CSSProperties>(() => ({
  position: 'sticky',
  bottom: '0',
  zIndex: 9,
  padding: '8px 8px 10px',
  background: 'transparent',
}))
</script>

<style scoped>
.voice-bar { pointer-events: none; }
.inner {
  pointer-events: auto;
  display: flex; align-items: center; gap: 12px;
  padding: 8px 10px;
}
.mic, .send {
  width: 40px; height: 40px;
  border-radius: 20px;
  border: 1px solid var(--border);
  background: var(--card-bg);
  color: var(--text-secondary);
}
.hint { flex: 1; color: var(--text-secondary); font-size: 13px; text-align: center; }
</style>
