<template>
  <div v-if="modelValue" class="overlay" @click.self="onClose">
    <div class="panel warm-card">
      <div class="head">
        <div class="tt">
          <div class="t1">天气备忘录</div>
          <div class="t2">懂你，服务会更好</div>
        </div>
        <button type="button" class="pill add" @click="emit('add')">添加</button>
      </div>

      <div v-if="list.length === 0" class="empty">暂无内容，点击“添加”开始</div>

      <ul v-else class="list">
        <li v-for="m in list" :key="m.id" class="cell">
          <div class="cell-main">
            <div class="text">{{ m.text }}</div>
            <div class="meta">{{ formatTime(m.createdAt) }}</div>
          </div>
          <div class="ops">
            <button type="button" class="pill" @click="emit('edit', m.id)">编辑</button>
            <button
              type="button"
              class="pill danger"
              @click="onRemoveClick(m.id)"
              title="删除该备忘录"
            >删除</button>
          </div>
        </li>
      </ul>

      <div class="foot">
        <button type="button" class="pill" @click="onClose">完成</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { SmartMemo } from '@/stores/memoStore'

interface Props {
  modelValue: boolean
  items?: SmartMemo[]
}
const props = withDefaults(defineProps<Props>(), { items: () => [] })
const emit = defineEmits<{
  (e: 'update:modelValue', v: boolean): void
  (e: 'add'): void
  (e: 'edit', id: string): void
  (e: 'remove', id: string): void
}>()

const list = computed(() => props.items || [])

function onClose() { emit('update:modelValue', false) }
function onRemoveClick(id: string) {
  if (confirm('确认删除该备忘录？')) emit('remove', id)
}
function formatTime(t: number) {
  const d = new Date(t)
  const mm = String(d.getMonth() + 1).padStart(2, '0')
  const dd = String(d.getDate()).padStart(2, '0')
  const hh = String(d.getHours()).padStart(2, '0')
  const mi = String(d.getMinutes()).padStart(2, '0')
  return `${d.getFullYear()}-${mm}-${dd} ${hh}:${mi}`
}
</script>

<style scoped>
.overlay { position: fixed; inset: 0; background: rgba(0,0,0,0.35); display:flex; align-items:center; justify-content:center; z-index: 50; }
.panel { width: min(720px, 94vw); padding: 12px; }

/* 头部对齐 iOS：标题+副标题在左，右侧“添加”是胶囊按钮 */
.head { display: flex; align-items: center; }
.tt { display:flex; flex-direction:column; gap:2px; }
.t1 { font-size: 17px; font-weight: 700; color: #000000; }
.t2 { font-size: 12px; color: #000000; }

/* 胶囊按钮（与 iOS 贴近）：轻描边、柔和渐变、圆角 999 */
.pill { padding: 6px 12px; border: 1px solid color-mix(in srgb, var(--border) 80%, transparent); background: var(--card-bg); color: #000000; border-radius: 999px; }
.pill.add { background: linear-gradient(180deg, rgba(255,255,255,0.98), rgba(255,255,255,0.9)); }
.pill.danger { background: #ff6b6b; color: #fff; border-color: transparent; }

.empty { padding: 12px; color: #000000; }

/* 列表样式：单元格卡片，外圆角大、内间距足够；主标题+时间+操作按钮 */
.list { list-style: none; padding: 0; margin: 10px 0 0; display: flex; flex-direction: column; gap: 10px; }
.cell { display: flex; align-items: center; gap: 10px; padding: 10px; border: 1px solid var(--border); border-radius: 16px; background: var(--card-bg); }
.cell-main { flex: 1 1 auto; }
.text { white-space: pre-wrap; color: #000000; font-size: 16px; font-weight: 600; }
.meta { margin-top: 4px; font-size: 12px; color: #000000; }
.ops { flex: 0 0 auto; display: flex; gap: 8px; }

.foot { margin-top: 12px; display: flex; justify-content: flex-end; }
</style>
