<template>
  <section class="memo warm-card">
    <div class="header">
      <div class="lhs">
        <span class="title">备忘录</span>
        <span class="subtitle">懂你，服务会更好</span>
      </div>
      <div class="rhs">
        <button class="icon-btn small warm" title="添加" aria-label="添加备忘录" @click="onAdd">
          <span class="icon-glyph">＋</span>
        </button>
        <button class="icon-btn small warm" title="查看列表" aria-label="查看所有备忘录" @click="showList = true">
          <span class="icon-glyph">⋯</span>
        </button>
      </div>
    </div>
    <!-- 标签/引导：与 iOS 一致，优先显示“当前未过期的智能备忘录”（此处以全部 memo 代替） -->
    <div class="tags" role="list" v-if="memoList.length > 0">
      <div v-for="m in memoList" :key="m.id" class="chip" role="listitem">{{ m.text }}</div>
    </div>
    <div class="tags" v-else>
      <div class="guide">
        <div class="guide-title">居住地、通勤、出行计划等信息可提升解读质量</div>
        <div class="guide-desc">点击右上角“＋”添加你的第一条备忘录</div>
      </div>
    </div>

    <!-- 弹窗：新增/编辑备忘录 -->
    <MemoEditorDialog
      v-model="showEditor"
      :editing-memo="editingMemo"
      :initial-text="initialText"
      @save="onSave"
      @delete="onDelete"
    />

    <!-- 弹窗：查看列表（支持编辑/删除/清空/继续添加） -->
    <MemoListDialog
      v-model="showList"
      :items="memoList"
      @add="onAdd"
      @edit="onEdit"
      @remove="onRemove"
    />
  </section>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { memoStore } from '@/stores/memoStore'
import { memoSamples, getCurrentBatch } from '@/data/memoSamples'
import type { SmartMemo } from '@/stores/memoStore'
import MemoEditorDialog from './MemoEditorDialog.vue'
import MemoListDialog from './MemoListDialog.vue'

const samplesBatch = ref<string[]>(getCurrentBatch(memoSamples, 0, 8))

// 弹窗状态
const showEditor = ref(false)
const showList = ref(false)
const editingMemo = ref<SmartMemo | null>(null)
const initialText = ref('')

// 列表（最新在前）
const memoList = computed(() => memoStore.list)

function onAdd() {
  editingMemo.value = null
  initialText.value = ''
  showList.value = false
  showEditor.value = true
}

function onAddWithText(text: string) {
  editingMemo.value = null
  initialText.value = text
  showList.value = false
  showEditor.value = true
}

function onEdit(id: string) {
  const m = memoList.value.find((i: SmartMemo) => i.id === id) || null
  editingMemo.value = m
  initialText.value = ''
  showList.value = false
  showEditor.value = true
}

function onSave(text: string) {
  if (editingMemo.value) {
    memoStore.update(editingMemo.value.id, text)
  } else {
    memoStore.add(text)
  }
  showEditor.value = false
}

function onDelete() {
  if (editingMemo.value) {
    memoStore.remove(editingMemo.value.id)
  }
  showEditor.value = false
}

function onRemove(id: string) {
  memoStore.remove(id)
}

// 按 iOS 设计，不提供“清空全部”入口
</script>

<style scoped>
/* 容器：使用设计系统的卡片样式 */
.memo {
  padding: 20px;
  background: var(--bg-glass);
  border: 2px solid var(--border-accent);
  border-radius: 20px;
  box-shadow: var(--shadow-strong);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}
.memo:hover {
  background: var(--bg-glass-hover);
  transform: translateY(-1px);
}

.header { display: flex; align-items: flex-end; }
.lhs { display: flex; align-items: baseline; gap: 6px; }
.rhs { margin-left: auto; display: flex; gap: 12px; }

/* 标题与副标题字号：增大以匹配设计系统 */
.title { font-size: 18px; font-weight: 700; color: #000000; }
.subtitle { font-size: 14px; color: #000000; opacity: 0.8; }

/* 圆形图标按钮：24x24，轻微内投影和高光，贴近 SF Symbols filled 风格 */
.icon-btn {
  width: 24px; height: 24px;
  display: inline-flex; align-items: center; justify-content: center;
  border: 1px solid color-mix(in srgb, var(--border) 80%, transparent);
  border-radius: 999px;
  background: linear-gradient(180deg, rgba(255,255,255,0.95), rgba(255,255,255,0.86));
  color: var(--text-primary);
  box-shadow:
    0 1px 1px rgba(0,0,0,0.06),
    inset 0 1px 0 rgba(255,255,255,0.7);
}
.icon-btn.small { width:20px; height:20px; }
.icon-btn.warm { color:#ff9800; border-color: rgba(255,152,0,0.35); background: linear-gradient(180deg, rgba(255,255,255,0.98), rgba(255,244,229,0.9)); }
.icon-btn:hover { filter: brightness(0.98); }
.icon-glyph { font-size: 16px; line-height: 1; }

/* 标签行：横向滚动，间距 10，与 iOS 圆角标签保持一致 */
.tags { display: flex; gap: 8px; overflow-x: auto; padding: 6px 0 2px; }

/* 标签：圆角12、细描边、柔和背景、阴影、14px 字号、8/14 内边距 */
.chip {
  flex: 0 0 auto;
  padding: 6px 10px;
  border-radius: 10px;
  border: 1px solid color-mix(in srgb, var(--border) 80%, transparent);
  background: linear-gradient(180deg, rgba(255,250,240,0.96), rgba(255,245,233,0.88));
  color: #000000;
  font-size: 11px;
  font-weight: 500;
  box-shadow:
    0 1px 1px rgba(0,0,0,0.03),
    0 2px 6px rgba(0,0,0,0.04);
}

/* 引导卡片：虚线框与淡渐变背景，圆角12 */
.guide {
  flex: 0 0 auto;
  min-width: 260px;
  padding: 8px 10px;
  border-radius: 12px;
  border: 1px dashed color-mix(in srgb, var(--border) 80%, transparent);
  background: linear-gradient(180deg, rgba(255,255,255,0.72), rgba(255,255,255,0.46));
  color: #000000;
}
.guide-title { font-size: 12px; font-weight: 500; color: #000000; }
.guide-desc { font-size: 12px; opacity: 0.8; color: #000000; }

/* 适配浅/深色：在不破坏变量体系下，尽量靠近 iOS 明暗层次 */
@media (prefers-color-scheme: dark) {
  .memo {
    background: color-mix(in srgb, var(--card-bg) 90%, #000);
    border-color: color-mix(in srgb, var(--border) 60%, transparent);
    box-shadow:
      0 0 0 transparent,
      0 8px 20px rgba(0,0,0,0.36);
  }
  .icon-btn {
    background: linear-gradient(180deg, rgba(255,255,255,0.08), rgba(255,255,255,0.04));
    color: var(--text-secondary);
    border-color: color-mix(in srgb, var(--border) 60%, transparent);
  }
  .chip {
    background: linear-gradient(180deg, rgba(255,255,255,0.06), rgba(255,255,255,0.03));
    border-color: color-mix(in srgb, var(--border) 60%, transparent);
    color: var(--text-secondary);
  }
  .guide {
    background: linear-gradient(180deg, rgba(255,255,255,0.06), rgba(255,255,255,0.03));
    border-color: color-mix(in srgb, var(--border) 60%, transparent);
    color: var(--text-secondary);
  }
}
</style>
