<template>
  <div ref="rootEl" class="butler-composer round-12 shadow-warm-md border-1">
    <div class="row">
      <div class="input-card">
        <img class="avatar user" :src="userAvatar" alt="me" />
        <div class="input-wrap">
          <input
            v-model="text"
            class="input"
            :placeholder="placeholder"
            @keydown.enter.exact.prevent="handleSend"
          />
          <div class="actions">
            <button class="send" :disabled="!text.trim()" @click="handleSend">发送</button>
            <button v-if="showVoice" class="voice" title="语音输入">🎙️</button>
          </div>
        </div>
      </div>
    </div>


    <div class="butler-note">
      <div class="note-text">{{ butlerNote }}</div>
      <img class="avatar butler small" :src="butlerAvatar" alt="butler" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { butlerStore } from '@/stores/butlerStore'

interface Props {
  userAvatar?: string
  butlerAvatar?: string
  placeholder?: string
  showVoice?: boolean
  butlerNote?: string
}

const props = withDefaults(defineProps<Props>(), {
  userAvatar: '/avatars/user.png',
  butlerAvatar: '/avatars/butler.png',
  placeholder: '和助手说点什么…',
  showVoice: true,
  butlerNote: '老董会陪同沟通，例如记录聊天历史、适时给些建议。',
})

const emit = defineEmits<{(e:'send', text: string): void}>()

const text = ref('')
const suggestions = computed(() => butlerStore.suggestions.value)

const rootEl = ref<HTMLElement | null>(null)

function handleSend() {
  const v = text.value.trim()
  if (!v) return
  emit('send', v)
  text.value = ''
}
function applySuggestion(t: string) {
  text.value = t
}
// 说明模块改为常驻，不再提供关闭按钮

onMounted(() => {
  // 默认给一点静态建议（若上层未设置）
  if (!butlerStore.suggestions.value.length) {
    butlerStore.setSuggestions([
      { id: 's1', text: '帮我看看今天需不需要带伞' },
      { id: 's2', text: '周末适合出游吗？' },
    ])
  }
})
</script>

<style scoped>
.butler-composer {
  position: sticky;
  bottom: 0;
  /* 玻璃薰衣草：外层更浅，半透明 + 模糊 */
  background: rgba(154, 128, 255, 0.17) !important;
  padding: 2px;
  /* 覆盖外层公共类，极轻薰衣草描边 */
  --border: rgba(154, 128, 255, 0.18);
  border: 1px solid var(--border) !important; /* 柔和描边 */
  border-top-color: var(--border) !important;
  border-radius: 10px !important;
  /* 更清晰：移除阴影，彻底避免模糊感 */
  box-shadow: none !important;
  backdrop-filter: blur(9px);
  -webkit-backdrop-filter: blur(9px);
  /* 与上部页面内容拉开间距 */
  margin-top: 6px;
}
.row { display:flex; align-items:center; }
.input-card {
  flex:1;
  display:flex; align-items:center; gap:6px;
  /* 玻璃薰衣草：子模块更深一档 */
  background: rgba(247, 246, 250, 0.21) !important;
  border: none; /* 子模块去描边，更加通透 */
  border-radius: 12px;
  padding: 3px;
  /* 去除模糊感：不使用阴影 */
  box-shadow: none;
}
.avatar { width:30px; height:30px; border-radius:50%; object-fit:cover; border:1px solid var(--border); }
.avatar.small { width:24px; height:24px; }
.input-wrap { flex:1; display:flex; align-items:center; gap:6px; }
.input { flex:1; height:26px; padding:0 8px; border:0; border-radius:8px; background: rgba(154,128,255,0.10) !important; color: #000000; }
.input::placeholder { color: #666666; }
.actions { display:flex; align-items:center; gap:6px; }
.send { height:26px; padding:0 10px; border-radius:8px; border:1px solid var(--border); background: rgba(154,128,255,0.10); }
.voice { height:26px; padding:0 8px; border-radius:8px; border:1px solid var(--border); background: rgba(154,128,255,0.10); }

.sugs { 
  display:flex; flex-wrap:wrap; gap:3px; margin-top:3px; 
  /* 玻璃薰衣草 */
  border: none !important; /* 子模块去描边 */
  border-radius: 8px;
  padding: 1px 50px;
}
.chip { font-size:8px; padding:1px;  border-radius:10px; background: rgba(255, 255, 128, 0.266) !important; box-shadow: none; }

.butler-note {
  margin-top:3px;
  display:flex; align-items:right; justify-content:flex-end; gap:6px; /* 贴近右侧头像 */
  font-size:10px; color: #000000;
  /* 容器透明，由内部气泡承担视觉 */
  background: transparent !important;
  border: none !important;
  border-left: 0;
  border-radius: 8px;
  padding: 0;
}
.note-text { 
  line-height:1.22; 
  /* 右侧对齐的对话气泡（自适应内容宽度） */
  display: inline-block;
  width: fit-content;
  background: rgba(154, 128, 255, 0.16) !important;
  border-radius: 12px;
  padding: 6px 10px; /* 比内容略宽 */
  max-width: 72%; /* 避免过长 */
  position: relative;
}
/* 右侧指向头像的小尾巴 */
.note-text::after {
  content: '';
  position: absolute;
  right: -6px;
  top: 12px;
  width: 0; height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-left: 6px solid rgba(154, 128, 255, 0.12); /* 与气泡底色一致 */
}

@media (prefers-color-scheme: dark) {
  .butler-composer {
    background: rgba(154, 128, 255, 0.24) !important;
  }
  .input-card {
    background: rgba(245, 245, 247, 0.2) !important;
    border-color: transparent;
  }
  .sugs {
    border-color: transparent !important;
  }
  .butler-note .note-text {
    background: rgba(233, 230, 244, 0.25) !important;
  }
}

</style>
