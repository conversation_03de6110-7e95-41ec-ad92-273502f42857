<template>
  <div v-if="visible" class="butler-float" :class="posClass" @click="handleClick">
    <img class="avatar" :src="avatar" alt="butler" />
    <div v-if="message" class="bubble">{{ message }}</div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  avatar?: string
  message?: string
  position?: 'right-bottom' | 'left-bottom' | 'right-top' | 'left-top'
  visible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  avatar: '/avatars/butler.png',
  message: '',
  position: 'right-bottom',
  visible: true,
})

const emit = defineEmits<{(e:'click'): void}>()

const posClass = computed(() => `pos-${props.position}`)

function handleClick() {
  emit('click')
}
</script>

<style scoped>
.butler-float {
  position: fixed;
  z-index: 50;
  display: flex;
  align-items: center;
  gap: 8px;
}
.pos-right-bottom { right: 16px; bottom: 20px; }
.pos-left-bottom { left: 16px; bottom: 20px; }
.pos-right-top { right: 16px; top: 20px; }
.pos-left-top { left: 16px; top: 20px; }

.avatar { width: 40px; height: 40px; border-radius: 50%; border:1px solid var(--border); background: var(--card-bg); object-fit: cover; box-shadow: 0 2px 8px rgba(0,0,0,0.12); }
.bubble { max-width: 52vw; font-size: 13px; line-height: 1.3; background: var(--card-bg); border:1px solid var(--border); border-radius: 12px; padding: 8px 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.08); }
</style>
