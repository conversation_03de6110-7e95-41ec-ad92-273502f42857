// AI 适配器：提供聊天、TTS、ASR 功能
// 临时实现，后续可以接入真实的AI服务

export interface IToolCall {
  id: string
  type: string
  function: {
    name: string
    arguments: string
  }
}

// 模拟的AI服务函数
export const chatStream = async function* (prompt: string): AsyncGenerator<string> {
  const responses = [
    "正在分析天气数据...",
    "根据当前气象条件，",
    "今日天气适宜外出，",
    "建议穿着轻薄衣物，",
    "注意防晒和补水。"
  ]

  for (const response of responses) {
    yield response
    await new Promise(resolve => setTimeout(resolve, 500))
  }
}

export const ttsStream = async function* (text: string): AsyncGenerator<Uint8Array> {
  // 模拟TTS流
  yield new Uint8Array([])
}

export const ttsAutoPlay = async (text: string): Promise<void> => {
  // 模拟TTS播放
  console.log('TTS播放:', text)
}

export const asrStream = async function* (): AsyncGenerator<string> {
  // 模拟ASR
  yield "语音识别功能暂未实现"
}

// —— 扩展：支持外部 OpenAI 兼容接口（如：美团 Sankuai 网关）
// 优先读取 Vite 环境变量；若未注入，则使用临时硬编码（仅开发应急，后续请还原为 env）
const ENV = (import.meta as any)?.env || {}
const AI_BASE = ENV.VITE_AI_API_BASE || 'https://aigc.sankuai.com'
const AI_KEY = ENV.VITE_AI_API_KEY || '1838824241643597850'
const AI_MODEL = ENV.VITE_AI_MODEL || 'gpt-4o-2024-08-06'

// 工具：SSE 文本解析（data: { json } -> choices[].delta/content）
function parseSSELines(text: string): string[] {
  return text
    .split(/\r?\n/)
    .map((l) => l.trim())
    .filter((l) => l.length > 0 && l.startsWith('data:'))
    .map((l) => l.slice(5).trim())
}

// 将 ReadableStream 转换为异步迭代文本块
async function* streamToAsyncIterable(resp: Response): AsyncIterable<string> {
  if (!resp.body) return
  const reader = resp.body.getReader()
  const decoder = new TextDecoder('utf-8')
  let buf = ''
  try {
    while (true) {
      const { value, done } = await reader.read()
      if (done) break
      buf += decoder.decode(value, { stream: true })
      // 尝试按双换行切分（SSE 事件边界）
      const parts = buf.split(/\n\n/)
      buf = parts.pop() || ''
      for (const p of parts) {
        const lines = parseSSELines(p)
        for (const d of lines) {
          if (d === '[DONE]') return
          try {
            const json = JSON.parse(d)
            // OpenAI 兼容：增量内容
            const delta = json?.choices?.[0]?.delta?.content
            if (typeof delta === 'string' && delta) {
              yield delta
            }
            // 兼容非增量（部分网关）
            const content = json?.choices?.[0]?.message?.content
            if (typeof content === 'string' && content) {
              yield content
            }
          } catch {
            // 忽略解析失败的片段
          }
        }
      }
    }
    // 处理残余缓冲
    if (buf) {
      const lines = parseSSELines(buf)
      for (const d of lines) {
        if (d === '[DONE]') return
        try {
          const json = JSON.parse(d)
          const delta = json?.choices?.[0]?.delta?.content
          if (typeof delta === 'string' && delta) yield delta
          const content = json?.choices?.[0]?.message?.content
          if (typeof content === 'string' && content) yield content
        } catch {}
      }
    }
  } finally {
    reader.releaseLock()
  }
}

// 非流式：直接返回完整文本
export async function runWeatherPrompt(prompt: string): Promise<string> {
  if (!AI_BASE || !AI_KEY) throw new Error('AI API 未配置：请设置 VITE_AI_API_BASE/VITE_AI_API_KEY')
  const url = `${AI_BASE.replace(/\/$/, '')}/v1/openai/native/chat/completions`
  const resp = await fetch(url, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${AI_KEY}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: AI_MODEL,
      stream: false,
      messages: [{ role: 'user', content: prompt }],
    }),
  })
  if (!resp.ok) throw new Error(`AI 接口错误：${resp.status}`)
  const data = await resp.json()
  const content = data?.choices?.[0]?.message?.content
  if (typeof content !== 'string') throw new Error('AI 响应不含内容')
  return content
}

// 流式：返回 AsyncIterable<string>
export async function* streamWeatherPrompt(prompt: string): AsyncIterable<string> {
  if (!AI_BASE || !AI_KEY) throw new Error('AI API 未配置：请设置 VITE_AI_API_BASE/VITE_AI_API_KEY')
  const url = `${AI_BASE.replace(/\/$/, '')}/v1/openai/native/chat/completions`
  const resp = await fetch(url, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${AI_KEY}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: AI_MODEL,
      stream: true,
      messages: [{ role: 'user', content: prompt }],
    }),
  })
  if (!resp.ok) throw new Error(`AI 接口错误：${resp.status}`)
  yield* streamToAsyncIterable(resp)
}
