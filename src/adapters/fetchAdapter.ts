// 只读复用 cankao 的通用 fetch 封装（含 access-token、401/403 处理等）
// 注意：不修改 cankao 源码，本项目仅通过别名 @cankao 引用
import fetchInstance, { getAccessToken as getCankaoAccessToken } from '@cankao/lib/fetch'

export const fetch = fetchInstance.fetch.bind(fetchInstance)
export const setAccessToken = (token: string) => fetchInstance.setAccessToken?.(token)
export const getAccessToken = () => getCankaoAccessToken?.()

export default fetchInstance
