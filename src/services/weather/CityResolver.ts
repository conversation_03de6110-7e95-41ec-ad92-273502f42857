// CityResolver：两层城市解析
// 1) 本地 Top100 映射（编译时内置，快速、无网络）
// 2) 懒加载 /poi/cities.json（放在 web/public/poi/ 下），命中后写入内存缓存
// 说明：不新增 iOS 没有的功能，仅工程层优化解析路径。

import { CITY_TOP100 } from '@/adapters/cityTop100'

export class CityResolver {
  private cache = new Map<string, string>() // cityNameZH -> locationId
  private loadedAll = false
  private citiesIndex: Array<{ name: string; locationId: string }> = []

  constructor() {
    // 预热 Top100
    for (const [name, id] of Object.entries(CITY_TOP100)) {
      this.cache.set(name, id)
    }
  }

  getSync(city: string): string | undefined {
    const key = city.trim()
    return this.cache.get(key)
  }

  async get(city: string): Promise<string | undefined> {
    const key = city.trim()
    const hit = this.cache.get(key)
    if (hit) return hit

    // 懒加载大表（一次）
    if (!this.loadedAll) {
      await this.ensureAllLoaded()
    }

    const found = this.citiesIndex.find((c) => c.name === key)
    if (found) {
      this.cache.set(key, found.locationId)
      return found.locationId
    }
    return undefined
  }

  private async ensureAllLoaded() {
    if (this.loadedAll) return
    try {
      const res = await fetch('/poi/cities.json')
      if (!res.ok) return
      const full = (await res.json()) as Array<{
        name: string
        locationId: string
      } |
        any>

      // 仅保留必要字段，避免占用过多内存
      this.citiesIndex = full
        .filter((x) => x && typeof x.name === 'string' && typeof x.locationId === 'string')
        .map((x) => ({ name: x.name, locationId: x.locationId }))
      this.loadedAll = true
    } catch (e) {
      // 静默失败，保持仅 Top100 可用
      console.warn('[CityResolver] load /poi/cities.json failed')
    }
  }
}

export const cityResolver = new CityResolver()
