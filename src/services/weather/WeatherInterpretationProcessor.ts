// WeatherInterpretationProcessor.ts
// 作用：
// - 构建 AI Prompt（天气 JSON + 备忘录文本 + 指令模板），严格中文风格
// - 解析 AI 返回，标准化为 interpretationText 与 cityInterpretations
// 注意：不新增 iOS 没有的功能；仅输出 iOS 同义的两个字段。

import type { LocationWeatherData } from '@/services/WeatherService'

export interface ParsedInterpretation {
  interpretationText: string
  cityInterpretations: Array<{ id?: string | number; cityName: string; text: string }>
}

export class WeatherInterpretationProcessor {
  /**
   * 构建提示词：优先对齐 iOS WeatherAIPromptBuilder.buildWeatherPrompt。
   * 为兼容当前数据流，保留原 cities/bundles/memoText 入参；
   * 如提供 context.weatherDataNL 等，则严格使用 iOS 模板；否则回退到精简版。
   */
  buildPrompt(
    cities: string[],
    bundles: Record<string, LocationWeatherData>,
    memoText: string,
    context?: {
      currentDate?: string
      memoTexts?: string[]
      currentLocationInfo?: string
      locationAnalysis?: string
      weatherDataNL?: string // 各城市天气数据（自然语言描述格式），含【北京天气情况】段落
    }
  ): string {
    const fallbackCityList = cities.join('、')
    const weatherJson = JSON.stringify(bundles, null, 2)
    const memo = memoText || ''

    // 如果给到与 iOS 对齐的上下文，则使用 iOS 模板
    if (context && (context.weatherDataNL || context.currentDate)) {
      const now = context.currentDate ?? new Date().toISOString()
      const memoTexts = context.memoTexts ?? (memo ? [memo] : [])
      const currentLocationInfo = context.currentLocationInfo ?? '当前所在地信息：未知'
      const locationAnalysis = context.locationAnalysis ?? '位置分析：暂无'
      const weatherDataNL = context.weatherDataNL ?? this.toNLFromBundles(bundles)
      const cityList = this.extractCityListFromWeatherData(weatherDataNL) || fallbackCityList

      return [
        '你是一个顶级的气象分析师，同时也是一个充满关怀、知识渊博、充满正能量的生活伙伴。',
        '你的任务是根据朋友的个人情况（备忘录、健康状况、出行计划）和详细的天气数据，',
        '为他们提供一份内容精干、数据翔实、充满人文关怀的个性化天气解读。',
        '',
        '【重要：需要解读的城市列表】：',
        '请严格按照以下城市列表进行解读，不要遗漏任何城市：',
        `${cityList}`,
        '',
        '【⚠️ 重要提醒】：',
        '- 必须解读上述列表中的每一个城市，不能遗漏任何一个',
        '- 每个城市都要单独解读，不要合并解读（如"北京海淀"只解读"北京海淀"，不要额外解读"北京"，除非用户备忘录另外提到了北京）',
        '- 如果城市列表中有7个城市，你的JSON响应中必须包含7个城市的解读',
        '- 不要因为某些城市天气相似就合并解读',
        '- 不要因为某些城市距离近就合并解读',
        '- 每个城市都有其独特的天气情况和人员关注点，必须分别解读',
        '- **不要提及数据来源**：解读中不要出现类似"(weather data from current weather, warnings, 24h forecast)"、"(weather data from warnings, 7-day forecast, AQI)"这样的数据来源说明',
        '- **避免使用英文**：尽量使用中文表达，避免使用英文术语，让用户更容易理解',
        '',
        '【重要：数据时效性说明】：',
        '- **当前天气**：1小时内最新数据，相对准确',
        '- **24小时预报**：12小时内获取，短期趋势可信',
        '- **3天预报**：24小时内获取，相对可靠',
        '- **7天预报**：24小时内获取，仅供参考，变化较大',
        '- **空气质量**：1小时内最新，时效性高',
        '- **🚨 预警信息**：24小时内获取，**请特别注意预警的具体时间范围和状态**',
        '  - **必须检查预警时间**：每个预警都有发布时间(pubTime)、开始时间(startTime)、结束时间(endTime)',
        '  - **过期预警排除**：如果预警结束时间已过或状态为"取消/解除"，**绝对不要在解读中提及此预警**',
        '  - **时间准确性**：只解读当前仍在生效期内的预警，避免误导用户',
        '  - **状态确认**：预警状态为"取消"、"解除"、"终止"的，视为无效预警',
        '- **生活指数**：24小时内获取，天气预报提供的建议，仅供参考，不要盲目按照这些建议进行解读用户需求',
        '',
        '【⚠️ 天气预报时间距离判断原则】：',
        '- **3天内**：数据相对可靠，可以详细解读',
        '- **3-7天**：数据仅供参考，重点提醒趋势，建议临近时再查看',
        '- **7天以上**：数据变化很大，不建议详细解读，只提醒用户关注趋势',
        '- **出差/旅行提醒**：如果备忘录提到未来7天以上的出行，应该提醒"时间较远，预报可能变化，建议出发前3-5天再查看详细预报"',
        '',
        '【核心原则】：',
        '1.  **专业气象专家身份**: 你是一个专业的气象分析师，不是情感顾问。保持专业、客观的语气，避免过于情感化的表达。不要使用类似"愿她们平安度过风雨，风雨之后总有晴空 🌈"等情感化表达。',
        '2.  **积极乐观**: 始终以积极、乐观的态度面对各种天气情况，体现社会主义核心价值观。',
        '3.  **时间感知**: 根据当前时间提供合适的建议（夜间不提醒紫外线等）。',
        '4.  **深度个性化与城市对应**: 严格将健康/需求与对应城市人员匹配。',
        '5.  **数据驱动**: 异常天气需给出数据支撑，表达通俗易懂。',
        '6.  **主动关怀**: 语气体贴、主动、有同理心。',
        '7.  **要事优先**: 先说最重要城市（当前所在地或有紧急情况的城市）。',
        '8.  **交通方式**: 仅当备忘录提及时给出对应提醒，不要猜测。',
        '9.  **工作日判断**: 仅在备忘录明确提及时提及相关安排。',
        '10. **时间精确**: 天气变化、预警需明确时间点。',
        '11. **承认局限性**: 承认天气数据的时空局限性。',
        '12. **关注预警/健康/出行/生活**: 但不幻想细节，不推断路况。',
        '17. **诚实可靠/避免误判/正能量表达/居住地表述规范/避免幻想用户具体情况** 等同上。',
        '',
        '【输出格式要求】：',
        '**必须返回JSON格式**，结构如下：',
        '{\n  "cities": [\n    {\n      "cityName": "城市名称",\n      "cityId": "城市的locationId（如101010100）",\n      "interpretation": "该城市的天气解读内容",\n      "weatherCondition": "该城市最突出的天气情况（从以下选项中选择：晴朗、多云、下雨、雷暴、刮风、下雪、雾霾)"\n    }\n  ]\n}',
        '',
        '【输出要求】：',
        '- 必须解读所有指定城市；严格按列表逐一输出；禁止合并或遗漏；每城≤200字；',
        '- 当前时间表述与温度表述需清晰区分实时与体感；',
        '- 语气自然，适当使用少量 emoji，避免 markdown；',
        '- 方便语音播报，读数采用“到”的口语化格式；',
        '',
        '【相对日期解析说明】：按备忘录添加/修改时间为基准解析“今天/明天”等相对日期。',
        '',
        '【输入信息】:',
        `- **当前时间**: ${now}`,
        `- **用户备忘录（含时间信息）**: ${this.formatMemoTextsByPerson(memoTexts)}`,
        `- **${currentLocationInfo}**`,
        `- **位置分析**: ${locationAnalysis}`,
        `- **各城市天气数据（自然语言描述格式）**: ${weatherDataNL}`,
        '',
        '请严格按照以上要求，生成专业、贴心、详细的天气解读。必须返回有效的JSON格式，不要包含任何其他文字。',
        '',
        '【最终检查清单】：',
        '✅ 确保JSON响应中包含所有城市列表中的城市',
        '✅ 确保每个城市都有独立的解读条目',
        '✅ 确保没有遗漏任何城市',
        '✅ 确保没有合并解读多个城市',
        '✅ 确保JSON格式正确，可以被解析',
      ].join('\n')
    }

    // 回退：精简提示（开发占位，避免中断功能）
    return [
      '你是一个严谨、克制的中文天气助理。请基于“天气数据➕个人情况（来自备忘录）”给出深度解读。',
      `目标城市：${fallbackCityList}`,
      '天气数据（按城市 locationId 索引）：',
      weatherJson,
      '个人备忘录文本：',
      memo,
      '请尽量返回 JSON：{"cities":[{"cityName":"北京","cityId":"101010100","interpretation":"..."}]}'
    ].join('\n')
  }

  parse(raw: string, fallbackCity?: string): ParsedInterpretation {
    // 1) 去掉 markdown 代码块围栏，或从文本中提取第一个平衡的 JSON 对象
    const obj = this.tryParseJSON(raw)
    if (obj && typeof obj === 'object') {
      // iOS 风格：{ cities: [ { cityName, cityId, interpretation, weatherCondition } ] }
      if (Array.isArray((obj as any).cities)) {
        const normalized = ((obj as any).cities as Array<any>)
          .map((c, i) => {
            const cityName = String(c.cityName ?? c.name ?? c.city ?? '')
            const text = String(c.interpretation ?? c.text ?? c.summary ?? '')
            const id = c.cityId ?? c.id ?? i
            return { id, cityName, text }
          })
          .filter((x) => x.cityName && x.text)
        if (normalized.length > 0) return { interpretationText: '', cityInterpretations: normalized }
      }

      // 兼容旧占位：{ interpretationText, cityInterpretations: [{ cityName, text }] }
      const interpretationText = (obj as any).interpretationText as string
      const cityInterpretations = Array.isArray((obj as any).cityInterpretations)
        ? ((obj as any).cityInterpretations as Array<any>)
        : []
      const normalized = cityInterpretations
        .map((x, i) => ({ id: x.id ?? i, cityName: String(x.cityName ?? x.name ?? ''), text: String(x.text ?? x.interpretation ?? '') }))
        .filter((x) => x.cityName && x.text)
      if (normalized.length > 0 || (typeof interpretationText === 'string' && interpretationText)) {
        return { interpretationText: interpretationText || '', cityInterpretations: normalized }
      }
    }

    // 2) 退化回退：当做一条主解释；若给到城市名则映射为单城市解释
    const text = (raw || '').trim()
    return {
      interpretationText: text,
      cityInterpretations: fallbackCity && text ? [{ id: 0, cityName: fallbackCity, text }] : [],
    }
  }

  // —— 工具：尽可能稳健地从原始文本中提取 JSON 对象
  private tryParseJSON(raw: string): any | null {
    if (!raw) return null
    const s = this.stripCodeFences(raw)
    // 先尝试直接解析（常见情况：内容就是 JSON 或前后仅空白/换行）
    try {
      return JSON.parse(s.trim())
    } catch {}
    // 从文本中提取第一个平衡的 { ... } 片段
    const frag = this.extractFirstJsonObject(s)
    if (frag) {
      try {
        return JSON.parse(frag)
      } catch {}
    }
    return null
  }

  // 去围栏：```json ... ``` 或 ``` ... ```；同时剔除行首的语言标记 json/jsonc 等
  private stripCodeFences(input: string): string {
    const fence = /```+[\s\S]*?```/g
    const m = fence.exec(input)
    if (m) {
      let inner = m[0]
      // 去掉包裹的 ``` 标记
      inner = inner.replace(/^```+\s*/,'').replace(/```+\s*$/,'')
      // 去掉开头可能出现的语言标记（json/jsonc 等）
      inner = inner.replace(/^(jsonc?|javascript|js|ts|txt)\s*/i, '')
      return inner
    }
    return input
  }

  // 从任意文本中找到第一个平衡的大括号 JSON 片段
  private extractFirstJsonObject(input: string): string | null {
    let start = -1
    let depth = 0
    let inStr: false | '"' | "'" = false
    let esc = false
    for (let i = 0; i < input.length; i++) {
      const ch = input[i]
      if (inStr) {
        if (esc) {
          esc = false
        } else if (ch === '\\') {
          esc = true
        } else if (ch === inStr) {
          inStr = false
        }
        continue
      }
      if (ch === '"' || ch === "'") {
        inStr = ch as any
        continue
      }
      if (ch === '{') {
        if (depth === 0) start = i
        depth++
      } else if (ch === '}') {
        depth--
        if (depth === 0 && start >= 0) {
          return input.slice(start, i + 1)
        }
      }
    }
    return null
  }

  // —— 辅助：从自然语言天气块提取城市列表（对齐 iOS 的正则）
  private extractCityListFromWeatherData(weatherData: string): string {
    try {
      const pattern = /【([^】]+)天气情况】/g
      const names: string[] = []
      const seen = new Set<string>()
      let m: RegExpExecArray | null
      while ((m = pattern.exec(weatherData)) !== null) {
        const name = m[1]
        if (!seen.has(name)) {
          seen.add(name)
          names.push(name)
        }
      }
      if (names.length) {
        return names.map((c, idx) => `${idx + 1}. ${c}`).join('\n')
      }
      return ''
    } catch {
      return ''
    }
  }

  // —— 辅助：将 yyyy-MM-dd 格式日期转为“今天/明天/后天/星期几”（对齐 iOS 表达）
  private formatDateToDayText(dateString: string): string {
    try {
      if (!dateString) return ''
      const [y, m, d] = dateString.split('-').map((s) => parseInt(s, 10))
      if (!y || !m || !d) return dateString
      const target = new Date(y, m - 1, d)
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const targetDay = new Date(target.getFullYear(), target.getMonth(), target.getDate())
      const diffDays = Math.round((targetDay.getTime() - today.getTime()) / (24 * 3600 * 1000))
      if (diffDays === 0) return '今天'
      if (diffDays === 1) return '明天'
      if (diffDays === 2) return '后天'
      const weekMap = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      return weekMap[targetDay.getDay()] || dateString
    } catch {
      return dateString
    }
  }

  // —— 辅助：当未提供自然语言天气块时，从 bundles 粗略生成一段 NL
  private toNLFromBundles(bundles: Record<string, LocationWeatherData>): string {
    try {
      const parts: string[] = []
      Object.entries(bundles).forEach(([locId, data]) => {
        const name = data?.cityName || locId
        const now = data?.currentWeather
        const fc = Array.isArray(data?.weatherForecast) ? data.weatherForecast : []

        let nowText = '当前天气数据有限'
        if (now) {
          const t = now.temp ? `${now.temp}°` : ''
          const feels = now.feelsLike ? `，体感${now.feelsLike}°` : ''
          const cond = now.text ? `${now.text}` : ''
          const wind = now.windDir && now.windScale ? `，${now.windDir}${now.windScale}级` : ''
          nowText = `当前${cond}${cond ? '，' : ''}气温${t}${feels}${wind}`
        }

        // 取未来最多3天的概览
        const days = fc.slice(0, 3)
        const daysText = days
          .map((d) => {
            const dayText = this.formatDateToDayText(d.fxDate)
            const range = d.tempMax && d.tempMin ? `${d.tempMax}°/${d.tempMin}°` : ''
            const desc = d.textDay || d.textNight || ''
            return `${dayText} ${range} ${desc}`.trim()
          })
          .filter(Boolean)
          .join('；')

        const futureText = daysText ? `；未来3天：${daysText}` : ''

        // 预警（若可用）
        let warningText = ''
        const warnings = Array.isArray((data as any)?.weatherWarnings) ? (data as any).weatherWarnings : []
        if (warnings.length > 0) {
          const titles = warnings.slice(0, 3).map((w: any) => w?.title).filter(Boolean).join('；')
          if (titles) warningText = `；预警：${titles}`
        }

        // 分钟级摘要（若可用）
        const minutely = (data as any)?.minutelySummary ? `；降水概览：${(data as any).minutelySummary}` : ''

        // 空气质量（若可用）
        let aqText = ''
        const aq = (data as any)?.airQuality
        if (aq && aq.aqi) {
          const partsAQ: string[] = []
          partsAQ.push(`AQI ${aq.aqi}`)
          if (aq.category) partsAQ.push(`${aq.category}`)
          if (aq.primary) partsAQ.push(`主污染物：${aq.primary}`)
          aqText = `；空气质量：${partsAQ.join('，')}`
        }

        // 逐时（若可用）取前2-3条
        let hourlyText = ''
        const hourly = Array.isArray((data as any)?.hourlyWeather) ? (data as any).hourlyWeather : []
        if (hourly.length > 0) {
          const seg = hourly.slice(0, 3).map((h: any) => {
            const time = typeof h?.fxTime === 'string' ? h.fxTime.slice(11, 16) : ''
            const t = h?.temp ? `${h.temp}°` : ''
            const desc = h?.text || ''
            return `${time}${time ? ' ' : ''}${desc}${desc ? '，' : ''}${t}`.trim()
          }).filter(Boolean).join('；')
          if (seg) hourlyText = `；逐时：${seg}`
        }

        parts.push(`【${name}天气情况】${nowText}${futureText}${warningText}${minutely}${aqText}${hourlyText}`)
      })
      return parts.join('\n')
    } catch {
      return ''
    }
  }

  // —— 辅助：格式化备忘录文本按“人员”分组的占位实现（对齐 iOS WeatherDataFormatter 行为的最小兼容）
  // 说明：iOS 使用 WeatherDataFormatter.formatMemoTextsByPerson(memoTexts)
  // 这里暂以简单编号列表形式输出，等待后续接入真实分组逻辑（不影响提示词语义）。
  private formatMemoTextsByPerson(memoTexts: string[]): string {
    try {
      if (!Array.isArray(memoTexts) || memoTexts.length === 0) return '无'
      return memoTexts
        .filter(Boolean)
        .map((t, i) => `${i + 1}. ${t}`)
        .join('\n')
    } catch {
      return '无'
    }
  }
}
