// 与 iOS MemoSampleData.getAllSampleMemos() 一致的示例数据
export const memoSamples: string[] = [
  // 地理位置类
  '我住在北京',
  '父母住在哈尔滨',
  '老婆去兰州旅游了',
  '明天我飞到上海出差',
  '孩子在广州上学',
  '朋友在深圳工作',
  '下周要去杭州开会',
  '爷爷奶奶在成都养老',

  // 健康状况类
  '我有关节炎',
  '我对花粉过敏',
  '我有哮喘病史',
  '我容易感冒',
  '我血压偏高',
  '我有风湿病',
  '我皮肤敏感',
  '我经常头疼',

  // 生活习惯类
  '我怕晒',
  '我开车上下班',
  '我经常跑步',
  '我喜欢下雨天',
  '我每天骑自行车',
  '我喜欢户外运动',
  '我经常熬夜',
  '我爱喝热茶',

  // 工作相关类
  '我是外卖员',
  '我在工地工作',
  '我经常出差',
  '我在户外工作',
  '我开滴滴',
  '我是快递员',
  '我在办公室工作',
  '我经常加班',

  // 特殊需求类
  '我周末要参加户外婚礼',
  '明天有重要面试',
  '周末要去爬山',
  '28日计划去海边度假',
  '明天上午要带孩子去公园',
  '周日晚上准备户外烧烤',
  '周六晚上要去看演唱会',
  '计划下周一拍婚纱照',

  // 季节偏好类
  '我喜欢春天',
  '我怕冷',
  '我怕热',
  '我喜欢雪天',
  '我讨厌潮湿',
  '我喜欢秋天',
  '我怕刮风',
  '我喜欢阴天',
]

export function getCurrentBatch(samples: string[], startIndex: number, batchSize = 8): string[] {
  const result: string[] = []
  for (let i = 0; i < batchSize; i++) {
    const index = (startIndex + i) % samples.length
    result.push(samples[index])
  }
  return result
}

export function getTotalBatches(samples: string[], batchSize = 8): number {
  return Math.max(1, Math.ceil(samples.length / batchSize))
}
