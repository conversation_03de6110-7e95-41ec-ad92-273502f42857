:root {
  --dynamic-bg: #f7f8fb;
  --card-bg: #ffffff;
  --text-primary: #000000;
  --text-secondary: #000000;
  --border: rgba(27,31,36,0.15);
  --primary: #4a8cff;
  --secondary: #ffb86b;
  --ai-accent: #7c5cff;
  --notable-accent: #ff7b72;
  --memo-accent: #4fb477;

  --shadow-warm-sm: 0 1px 2px rgba(0,0,0,0.06), 0 1px 1px rgba(0,0,0,0.04);
  --shadow-warm-md: 0 2px 6px rgba(0,0,0,0.08), 0 6px 12px rgba(0,0,0,0.06);
}

html, body, #app {
  height: 100%;
  background: var(--dynamic-bg);
  color: var(--text-primary);
}

.warm-card {
  background: var(--card-bg);
  border: 1px solid var(--border);
  border-radius: 12px;
  box-shadow: var(--shadow-warm-sm);
}

.header-gradient {
  background: linear-gradient(135deg, color-mix(in srgb, var(--primary) 10%, transparent), color-mix(in srgb, var(--secondary) 10%, transparent));
}

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.border-1 { border: 1px solid var(--border); }
.round-12 { border-radius: 12px; }
.shadow-warm-md { box-shadow: var(--shadow-warm-md); }
.flex-center { display:flex; align-items:center; }
