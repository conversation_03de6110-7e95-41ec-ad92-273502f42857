export function copyText(text: string): Promise<void> {
  if (typeof navigator !== 'undefined' && navigator?.clipboard?.writeText) {
    return navigator.clipboard.writeText(text)
  }
  const ta = document.createElement('textarea')
  ta.value = text
  ta.style.position = 'fixed'
  ta.style.opacity = '0'
  document.body.appendChild(ta)
  ta.select()
  try { document.execCommand('copy') } catch {}
  document.body.removeChild(ta)
  return Promise.resolve()
}

export async function shareText(title: string, text: string): Promise<'shared' | 'copied'> {
  if (typeof navigator !== 'undefined' && (navigator as any)?.share) {
    try {
      await (navigator as any).share({ title, text })
      return 'shared'
    } catch {}
  }
  await copyText(text)
  return 'copied'
}

export function useShare() {
  return {
    copyText,
    shareText,
    showDetailsPlaceholder() {
      alert('“查看详情”功能稍后接入（对齐 iOS 的详情页）')
    },
    async copyWithToast(text: string) {
      await copyText(text)
      alert('内容已复制到剪贴板')
    },
    async shareWithFallback(title: string, text: string) {
      const mode = await shareText(title, text)
      if (mode === 'copied') {
        alert('已复制到剪贴板（系统分享稍后接入）')
      }
    }
  }
}
